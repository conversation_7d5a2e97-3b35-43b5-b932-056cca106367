package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.agent.AgentConvertDto;
import com.enttribe.promptanalyzer.dto.agent.AgentDto;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.model.AgentHistory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing agent-related operations.
 * Provides endpoints for creating agents, searching agents, and managing Ni<PERSON>i triggers.
 *
 * <AUTHOR>
 * @version 1.0
 */

@FeignClient(name = "AgentRest", url = "${prompt-analyzer-service.url}", path = "/agent", primary = false)
public interface AgentRest {

    /**
     * Creates a new agent.
     * Requires agent creation and write permissions.
     *
     * @param agent The agent details to create
     * @return Map containing the result of the operation
     */
    @Operation(
            summary = "Create new agent",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME,
                            scopes = {
                                    APIConstants.ROLE_API_AGENT_WRITE,
                                    APIConstants.ROLE_API_PROMPT_READ,
                                    APIConstants.ROLE_API_TOOL_READ
                            })})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Agent created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/create")
    Map<String, String> create(@RequestBody AgentDto agent);

    /**
     * Searches for agents based on provided filters and pagination parameters.
     *
     * @param filter    Optional filter string to search agents
     * @param offset    Optional pagination offset
     * @param size      Optional pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching agents converted to DTOs
     */
    @Operation(summary = "Search for agents",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_AGENT_READ})})
    @ApiResponses(value =
            {@ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Agents retrieved successfully"),
                    @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("search")
    List<AgentConvertDto> search(@RequestParam(required = false) String filter, @RequestParam(required = false) Integer offset, @RequestParam(required = false) Integer size, @RequestParam(required = false) String orderBy, @RequestParam(required = false) String orderType);

    /**
     * Generates a plan based on user query for custom agent.
     *
     * @param agent Custom agent details containing the user query
     * @return Map containing the generated plan response
     */
    @Operation(
            summary = "Generate plan for user query",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_AGENT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Plan generated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("getPlanforUserQuery")
    Map<String, String> getPlanforUserQuery(@RequestBody CustomAgentDto agent);


    @GetMapping("/getAgentHistory")
    AgentHistory getAgentHistory(@RequestParam(name = "processGroupId") String processGroupId);

    /**
     * Creates a trigger in NiFi based on flow configuration.
     *
     * @param flowDto Flow configuration details
     * @return Map containing the result of trigger creation
     */
    @Operation(
            summary = "Create trigger in NiFi",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_AGENT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Trigger created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("createTriggerInNifi")
    Map<String, String> createTriggerInNifi(@RequestBody NifiFlowDto flowDto);

    /**
     * Generates a trigger name and description based on user query.
     *
     * @param requestBody Map containing the user query under "userQuery" key
     * @return Map containing the generated trigger name and description
     */
    @Operation(
            summary = "Create trigger name",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_AGENT_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Trigger name created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("createTriggerName")
    Map<String, String> createTriggerName(@RequestBody Map<String, String> requestBody);
}




