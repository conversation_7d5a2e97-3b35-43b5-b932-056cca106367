package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.HashMap;
import java.util.List;

/**
 * REST controller for managing tool callback provider operations.
 * Provides endpoints for retrieving tool callback information.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
        name = "ToolCallbackProviderRest",
        url = "${prompt-analyzer-service.url}",
        primary = false
)
public interface ToolCallbackProviderRest {

    /**
     * Fetches a list of tools from the external service.
     * Requires ROLE_API_TOOL_CALLBACK_READ security role.
     *
     * @return A list of tools as a list of hash maps.
     */
    @Operation(
            summary = "Get list of tools",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TOOL_CALLBACK_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tools retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/getListOfTools")
    List<HashMap<String, String>> getToolCallbackProvider();
}
