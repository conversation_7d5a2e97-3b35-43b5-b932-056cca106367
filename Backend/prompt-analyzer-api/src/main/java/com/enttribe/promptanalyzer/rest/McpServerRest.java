package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.model.McpServer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing MCP server operations.
 * Provides endpoints for creating, updating, searching, counting, and deleting MCP servers.
 *
 * <AUTHOR>
 * @version 1.0
 */

@FeignClient(name = "McpServerRest", url = "${prompt-analyzer-service.url}", path = "/mcpserver", primary = false)
public interface McpServerRest {

    /**
     * Creates a new MCP server.
     *
     * @param serverDto The MCP server details to create
     * @return Map containing the result of the operation
     */
    @Operation(
            summary = "Create new MCP server",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_MCP_SERVER_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP server created successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/create")
    Map<String, String> create(@RequestBody McpServerDto serverDto);

    /**
     * Updates an existing MCP server.
     *
     * @param serverDto The MCP server details to update
     * @return Map containing the result of the operation
     */
    @Operation(
            summary = "Update MCP server",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_MCP_SERVER_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP server updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/update")
    Map<String, String> update(@RequestBody McpServerDto serverDto);

    /**
     * Searches for MCP servers based on provided filters and pagination parameters.
     *
     * @param filter    Optional filter string to search servers
     * @param offset    Optional pagination offset
     * @param size      Optional pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching MCP servers
     */
    @Operation(summary = "Search for MCP servers",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_MCP_SERVER_READ})})
    @ApiResponses(value =
            {@ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP servers retrieved successfully"),
                    @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/search")
    List<McpServer> search(@RequestParam(required = false) String filter,
                           @RequestParam(required = false) Integer offset,
                           @RequestParam(required = false) Integer size,
                           @RequestParam(required = false) String orderBy,
                           @RequestParam(required = false) String orderType);

    /**
     * Counts MCP servers based on provided filter.
     *
     * @param filter Optional filter string to count matching servers
     * @return Map containing the count of matching servers
     */
    @Operation(summary = "Count MCP servers",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_MCP_SERVER_READ})})
    @ApiResponses(value =
            {@ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP servers counted successfully"),
                    @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/count")
    Long count(@RequestParam(required = false) String filter);

    /**
     * Soft deletes an MCP server by ID.
     *
     * @param id The ID of the MCP server to delete
     * @return Map containing the result of the operation
     */
    @Operation(
            summary = "Delete MCP server",
            security = {
                    @SecurityRequirement(name = APIConstants.DEFAULT_SCHEME, scopes = {APIConstants.ROLE_API_MCP_SERVER_WRITE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP server deleted successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping("/delete/{id}")
    Map<String, String> softDelete(@PathVariable Integer id);

    /**
     * Retrieves MCP servers by their IDs.
     * Requires ROLE_API_MCP_SERVER_READ security role.
     *
     * @param ids List of MCP server IDs to retrieve
     * @return List of McpServerDto containing the server details
     */
    @Operation(
            summary = "Retrieve MCP servers by IDs",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_MCP_SERVER_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "MCP servers retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/getMcpServerByIds")
    List<McpServerDto> getMcpServerByIds(@RequestBody List<Integer> ids);




}