package com.enttribe.promptanalyzer.constants;

/**
 * Constants used throughout the API for authentication, authorization, and responses.
 * Contains role definitions, status codes, and access scopes for different API endpoints.
 * Author: VisionWaves
 * Version: 1.0
 */
public class APIConstants {
    
    private APIConstants() {
    }

    public static final String RESULT = "result";
    public static final String SUCCESS= "success";
    public static final String FAILED = "failed";
    public static final String SUCCESS_CODE = "200";
    public static final String DEFAULT_SCHEME = "default";
    public static final String ERROR= "Internal server error";
    public static final String ERROR_CODE = "500";

    // Tool scopes
    public static final String ROLE_API_TOOL_WRITE = "Write access for Tool data";
    public static final String ROLE_API_TOOL_READ = "Read access for Tool data";

    // LlmModel scopes
    public static final String ROLE_API_LLMMODEL_WRITE = "Write access for LlmModel data";
    public static final String ROLE_API_LLMMODEL_READ = "Read access for LlmModel data";

    // Test case scopes
    public static final String ROLE_API_TESTCASE_WRITE = "Write access for TestCase data";
    public static final String ROLE_API_TESTCASE_GENERATE = "ROLE_API_TESTCASE_GENERATE";
    public static final String ROLE_API_TESTCASE_READ = "Read access for TestCase data";

    // Exception scopes
    public static final String ROLE_API_EXCEPTION_SAVE= "Exception audit and Prompt Audit exception save";
    public static final String ROLE_API_EXCEPTION_READ = "Read Prompt audit list";

    // Llm API scopes
    public static final String ROLE_API_CHAT_COMPLETE= "ROLE_API_CHAT_COMPLETE";
    public static final String ROLE_API_EXECUTE_PROMPT = "ROLE_API_EXECUTE_PROMPT";
    public static final String ROLE_API_EXECUTE_PROMPT_V1 = "ROLE_API_EXECUTE_PROMPT_V1";


    // trigger API scopes
    public static final String ROLE_API_TRIGGER_READ = "Read access for Trigger data";
    public static final String ROLE_API_TRIGGER_WRITE = "Write access for Trigger data" ;

    // Agent API scopes
    public static final String ROLE_API_AGENT_READ = "Read access for Agent data";
    public static final String ROLE_API_AGENT_WRITE = "Write access for Agent data";

    // Processor API scopes
    public static final String ROLE_API_PROCESSOR_READ = "Read access for Processor data";
    public static final String ROLE_API_PROCESSOR_WRITE = "Write access for Processor data";

    // Query API scopes
    public static final String ROLE_API_QUERY_READ = "Read access for Query data";
    public static final String ROLE_API_QUERY_WRITE = "Write access for Query data";

    // Tag API scopes
    public static final String ROLE_API_TAG_READ = "Read access for Tag data";
    public static final String ROLE_API_TAG_WRITE = "Write access for Tag data";

    // Prompt API scopes
    public static final String ROLE_API_PROMPT_READ = "Read access for Prompt data";
    public static final String ROLE_API_PROMPT_WRITE = "Write access for Prompt data";

    //
    public static final String ROLE_API_KNOWLEDGE_BASE_WRITE = "Read access for Knowledge Base data";
    public static final String ROLE_API_KNOWLEDGE_BASE_READ = "Write access for Knowledge Base data";

    // MCP Server scopes
    public static final String ROLE_API_MCP_SERVER_READ = "Read access for MCP Server data";
    public static final String ROLE_API_MCP_SERVER_WRITE = "Write access for MCP Server data";

    // hint scopes
    public static final String ROLE_API_HINT_WRITE = "Write access for Hint data";
    public static final String ROLE_API_HINT_READ = "Read access for Hint data" ;
}

