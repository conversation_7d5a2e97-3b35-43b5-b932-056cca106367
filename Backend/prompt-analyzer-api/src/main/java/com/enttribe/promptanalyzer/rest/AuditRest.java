package com.enttribe.promptanalyzer.rest;


import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing audit-related operations.
 * Provides endpoints for saving and retrieving exception and prompt audit records.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "AuditRest", url = "${prompt-analyzer-service.url}", path = "/audit", primary = false)
public interface AuditRest {


    /**
     * Saves an exception audit record.
     * Requires ROLE_API_AUDIT_EXCEPTION_SAVE security role.
     *
     * @param exceptionAuditDto The exception audit details to save
     * @return String containing the audit ID of the saved record
     * @apiNote Response Codes:
     * 200 - Save exception audit successfully
     * 500 - Error occurred during save operation
     */
    @Operation(summary = "Save exception", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_AUDIT_EXCEPTION_SAVE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Save exception audit successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/exception/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    String saveExceptionAudit(@RequestBody ExceptionAuditDto exceptionAuditDto);

    /**
     * Saves a prompt audit record.
     * Requires ROLE_API_EXCEPTION_SAVE security role.
     *
     * @param promptAuditDto The prompt audit details to save
     * @return String containing the audit ID of the saved record
     * @apiNote Response Codes:
     * 200 - Save Prompt Audit exception successfully
     * 500 - Error occurred during save operation
     */
    @Operation(summary = "Save Prompt Audit exception", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_SAVE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Save Prompt Audit exception successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/prompt/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    String savePromptAudit(@RequestBody PromptAuditDto promptAuditDto);

    /**
     * Retrieves a list of prompt audit records for a specific audit ID.
     * Requires ROLE_API_EXCEPTION_READ security role.
     *
     * @param auditId The audit ID to retrieve records for
     * @return List of PromptAudit records matching the audit ID
     * @apiNote Response Codes:
     * 200 - Get Prompt Audit List successfully
     * 500 - Error occurred during retrieval
     */
    @Operation(summary = "Get Prompt Audit List", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Prompt Audit List successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/getPromptAuditListByAuditId/{auditId}")
    List<PromptAudit> getPromptAuditListByAuditId(@PathVariable String auditId);

    @Operation(summary = "Get Prompt Audit List By Prompt Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Prompt Audit List By Prompt Id successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/getPromptAuditListByPromptId/{promptId}")
    List<PromptAudit> getPromptAuditListByPromptId(@PathVariable String promptId);

    /**
     * search Prompt Audit records
     * @param filter
     * @param offset
     * @param size
     * @param orderBy
     * @param orderType
     * @return
     */
    @Operation(summary = "Search Prompt Audit", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Search Prompt Audit successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping(path = "prompt/search")
    List<PromptAuditDto> searchPromptAudit(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * count Prompt Audit records
     * @param filter
     * @return
     */
    @Operation(summary = "Count Prompt Audit", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count Prompt Audit successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping(path = "prompt/count")
    Long countPromptAudit(@RequestParam(required = false) String filter);

    @Operation(summary = "Save Tool Audit", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_SAVE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Save Tool Audit successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/toolAudit/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> saveToolAudit(@RequestBody ToolAuditDto toolAuditDto);

    @Operation(summary = "Get Tool Audit List By Audit Id", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_EXCEPTION_READ})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Get Tool Audit List By Audit Id successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping("/getToolAuditListByAuditId/{auditId}")
    List<ToolAudit> getToolAuditListByAuditId(@PathVariable String auditId);

}

